module.exports = {
    apps: [
        {
            name: 'frontend',
            script: 'yarn',
            args: 'start',
            cwd: '/home/<USER>/udoymart',
            instances: 1,
            // ======= Retry & Restart Configuration =======
            autorestart: true,       // Auto-restart on crashes
            max_restarts: 5,        // Max restart attempts
            min_uptime: 30000,       // Consider app "stable" if runs for 5s
            restart_delay: 3000,    // 3s delay between restarts
            exp_backoff_restart_delay: 100, // Enable exponential backoff
            max_memory_restart: '1740M',
            // ======== Startup Failure Detection ========
            listen_timeout: 10000,  // Wait 10s for app to start
            wait_ready: true,       // Wait for Node.js "ready" signal
            // ============================================
            env: {
                NODE_ENV: 'production',
                PORT: 3000,
                NEXT_DIST_DIR: '.next'
            },
            error_file: './log/pm2/udoymart-frontend-error.log',
            out_file: './log/pm2/udoymart-frontend-out.log'
        }
    ]
};