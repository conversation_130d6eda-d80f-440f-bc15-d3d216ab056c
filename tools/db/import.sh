#!/bin/bash

(
    # Find Postgres container
    DB_CONTAINER=$(docker ps | grep postgres | sed 's/ \+/\n/g' | grep postgresql | grep -v bitnami)

    # Load environment variables
    export $(grep -v '^#' .env | grep -v "*" | xargs -d '\n')

    docker exec -e PGPASSWORD="$POSTGRES_PASS" -i "$DB_CONTAINER" \
        pg_restore --verbose --clean --no-acl --no-owner -h localhost -U "$POSTGRES_USERNAME" -d "$POSTGRES_DATABASE"
)
