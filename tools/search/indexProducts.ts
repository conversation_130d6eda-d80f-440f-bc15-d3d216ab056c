import { PrismaClient } from "@prisma/client";
import { wait } from "../../src/utils/wait";
import { Index, MeiliSearch, RecordAny } from "meilisearch";

async function waitForEnd(
  index: Index<RecordAny>,
  updateId: number,
  name: string
) {
  const deleteTask = await index.tasks.deleteTasks({
    uids: [updateId],
  });

  while (true) {
    const status = await index.tasks.getTask(deleteTask.taskUid);
    if (
      status.status === "succeeded" ||
      status.status === "failed" ||
      status.status === "canceled"
    ) {
      break;
    }

    console.log(`Waiting for ${name} task to complete...`);
    await wait(1000);
  }
}

async function main() {
  const clent = new MeiliSearch({
    host: process.env.MEILI_HOST!,
    apiKey: process.env.MEILI_MASTER_KEY!,
  });
  const prisma = new PrismaClient();
  const index = clent.index("products");

  const deleteStatus = await index.deleteAllDocuments();

  await waitForEnd(index, deleteStatus.taskUid, "delete");

  const products = await prisma.product.findMany({
    where: {
      hide: false,
    },
    select: {
      id: true,
      name: true,
      nam: true,
      titleBe: true,
      description: true,
      biboron: true,
      category: {
        select: {
          name: true,
          nam: true,
        },
      },
    },
  });

  // After creating the index, configure ranking rules
  const settingsUpdate = await index.updateSettings({
    // 1. A better ranking rule order
    rankingRules: [
      "words",
      "typo",
      "proximity",
      "attribute",
      "exactness",
      "sort",
      // "sales:desc" // Example: Add custom ranking for business logic
    ],
    // 2. Define which attributes to search on (order matters!)
    searchableAttributes: [
      "name",
      "titleBe",
      "nam",
      "description",
      "biboron",
      "categoryEn",
      "categoryBn",
    ],
    // 3. Define attributes for filtering and faceting
    // filterableAttributes: [
    //   "category",
    //   "brand",
    //   "price", // Example attributes
    // ],
    // 4. Define attributes that can be used for sorting
    // sortableAttributes: [
    //   "price",
    //   "createdAt", // Example attributes
    // ],
    // 5. Enable typo tolerance (as you had)
    typoTolerance: {
      enabled: true,
    },
    stopWords: [
      "the",
      "a",
      "an",
      "of",
      "in", // English
      "এবং",
      "এর",
      "একটি",
      "ও", // Bengali Example
    ],
  });

  await waitForEnd(index, settingsUpdate.taskUid, "settings update");

  const mapped = products.map((product) => ({
    ...product,
    categoryEn: product.category?.name || "",
    categoryBn: product.category?.nam || "",
  }));

  const addStatus = await index.addDocuments(mapped, { primaryKey: "id" });

  await waitForEnd(index, addStatus.taskUid, "add");

  console.log("Products Indexed!");
}

main();
