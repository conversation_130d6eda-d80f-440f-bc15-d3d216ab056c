import "dotenv/config";
import { $ } from "zx";
import chalk from "chalk";
import { log, error } from "console";

const logs: string[] = [];
const ok = (msg: string) => {
  logs.push(msg);
  log(chalk.greenBright(msg));
};
$.verbose = false;

async function getComitMessages(lastCommit: string) {
  const commits =
    await $`git log ${lastCommit}..HEAD --pretty=format:"%H|||%an|||%s"`;
  const messages = commits.stdout
    .trim()
    .split("\n")
    .map((line) => {
      const [hash, name, message] = line.split("|||");
      return { hash, name, message };
    });
  return messages || [];
}

async function sendSuccessMessage(lastCommit: string) {
  const latestCommit = await $`git rev-parse HEAD`;
  const currentBranch = await $`git rev-parse --abbrev-ref HEAD`;
  const messages = await getComitMessages(lastCommit);

  const embed = {
    title: "Deployment Details 📝",
    color: 0x00ff00, // Green color
    fields: [
      { name: "Branch Name", value: currentBranch.stdout.trim() },
      { name: "Commit Hash", value: latestCommit.stdout.trim() },
      {
        name: "Changes Since Last Deployment",
        value:
          messages.length === 0
            ? "No changes since last deployment"
            : messages.map((item) => `- ${item.message}`).join("\n"),
      },
    ],
    timestamp: new Date().toISOString(),
  };

  try {
    await fetch(process.env.DISCORD_CICD_WEBHOOK!, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        content: "Udoymart Deployed Successfully 🚀",
        embeds: [embed],
      }),
    });
  } catch (error) {
    console.error("Failed to send Discord notification:", error);
  }
}

async function sendErrorMessage(error: any) {
  try {
    await fetch(process.env.DISCORD_CICD_WEBHOOK!, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        content: "Udoymart Deployment Failed ❌",
        embeds: [
          {
            title: "Deployment Details 📝",
            color: 0xff0000, // Red color
            fields: [
              { name: "Error", value: error?.message },
              {
                name: "Steps Completed",
                value: logs.map((item) => `✅ ${item}`).join("\n"),
              },
            ],
            timestamp: new Date().toISOString(),
          },
        ],
      }),
    });
  } catch (error) {
    console.error("Failed to send Discord notification:", error);
  }
}

async function main() {
  try {
    const currentBranch = await $`git rev-parse --abbrev-ref HEAD`;
    // Collect the current commit hash before pull
    const currentCommit = await $`git rev-parse HEAD`;
    ok(`Pulling from ${currentBranch}`);
    await $`git pull`;

    await $`rm -rf temp`;

    ok("Install dependencies");
    $.verbose = true;
    await $`yarn install`;

    ok("Applying Database Migrations");
    await $`yarn prisma migrate deploy`;
    await $`yarn prisma generate`;

    ok("Building Frontend Application");

    await $`NEXT_DIST_DIR=temp NODE_OPTIONS="--max-old-space-size=8192" yarn build`;

    // Replaces temp folder with .next
    ok("Stop Frontend Service");
    await $`pm2 stop frontend`;

    ok("Copying build files");
    await $`rm -rf .next && mv temp .next`;

    ok("Reloading Frontend Service");
    await $`pm2 start frontend`;

    ok("Updating Search Index");
    await $`yarn index:products`;

    ok("Deployment Complete");
    $.verbose = false;
    await sendSuccessMessage(currentCommit.stdout.trim());
  } catch (err: any) {
    error(err);
    await sendErrorMessage(err);
    throw new Error(err);
  }
}

main();
