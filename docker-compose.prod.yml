x-common-config: &config
  init: true
  restart: always
  env_file:
    - .env
  profiles: [ production ]

services:
  postgresql:
    image: bitnami/postgresql:latest
    restart: always
    <<: *config
    environment:
      - POSTGRESQL_USERNAME=${POSTGRES_USERNAME}
      - POSTGRESQL_PASSWORD=${POSTGRES_PASS}
      - POSTGRESQL_DATABASE=${POSTGRES_DATABASE}
      - POSTGRESQL_POSTGRES_PASS=${POSTGRES_ADMIN}
    volumes:
      - postgresql:/bitnami/postgresql
    tmpfs:
      - /var/lib/pg_stat_tmp
    ports:
      - "5433:5432"
    networks:
      - app-network

  meilisearch:
    image: getmeili/meilisearch:latest
    restart: always
    <<: *config
    environment:
      - MEILI_ENV=production
      - MEILI_NO_ANALYTICS=true
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY} # Add to your .env file
    volumes:
      - meilisearch_data:/data.ms
    ports:
      - "7700:7700"
    networks:
      - app-network
    # deploy:
    #   resources:
    #     limits:
    #       cpus: '1.0'
    #       memory: 512M

  nginx:
    image: nginx
    restart: always
    <<: *config
    volumes:
      - ./tools/nginx/default.conf:/etc/nginx/templates/default.conf.template
      - ./tools/nginx/502.html:/var/www/html/maintenance/502.html
      - ./static-dir:/var/www/static-dir
      - ./certbot_conf:/etc/letsencrypt
      - ./certbot_www:/var/www/certbot
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DOCKER_HOST_IP=${DOCKER_HOST_IP}
      - NGINX_PORT=80
    depends_on:
      - postgresql
      - meilisearch # Added dependency
    networks:
      - app-network

  certbot:
    image: certbot/certbot
    restart: unless-stopped
    <<: *config
    volumes:
      - ./certbot_conf:/etc/letsencrypt
      - ./certbot_www:/var/www/certbot
    command: renew --quiet --webroot --webroot-path=/var/www/certbot
    networks:
      - app-network

volumes:
  postgresql:
  meilisearch_data: # Added volume for Meilisearch
  certbot_conf:
  certbot_www:


networks:
  app-network:
    driver: bridge
