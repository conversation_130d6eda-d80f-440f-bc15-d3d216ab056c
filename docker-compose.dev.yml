x-common-config: &config
  init: true
  restart: always
  env_file:
    - .env
  profiles: [ development ]

services:
  postgresql_dev:
    image: bitnami/postgresql:latest
    restart: always
    <<: *config
    environment:
      - POSTGRESQL_USERNAME=${POSTGRES_USERNAME}
      - POSTGRESQL_PASSWORD=${POSTGRES_PASS}
      - POSTGRESQL_DATABASE=${POSTGRES_DATABASE}
      - POSTGRESQL_POSTGRES_PASS=${POSTGRES_ADMIN}
    volumes:
      - postgresql:/bitnami/postgresql
    tmpfs:
      - /var/lib/pg_stat_tmp
      # - /bitnami/postgresql
    ports:
      - 5433:5432

  meilisearch_dev:
    image: getmeili/meilisearch:latest
    restart: always
    <<: *config
    environment:
      - MEILI_ENV=production
      - MEILI_NO_ANALYTICS=true
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY} # Add to your .env file
    volumes:
      - meilisearch_data:/data.ms
    ports:
      - "7700:7700"
    networks:
      - app-network
    # deploy:
    #   resources:
    #     limits:
    #       cpus: '1.0'
    #       memory: 512M

  nginx_dev:
    image: nginx # Using the official Nginx image
    restart: always
    <<: *config
    volumes:
      # This volume mounts your custom Nginx configuration template.
      # Nginx will process this template using envsubst to replace environment variables.
      - ./tools/nginx/dev.conf:/etc/nginx/templates/default.conf.template
      - ./tools/nginx/502.html:/var/www/html/maintenance/502.html
      - ./static-dir:/var/www/static-dir # For serving static files directly via Nginx (optional)
    ports:
      # Map host port 80 to container port 80
      - '80:80'
    environment:
      # This environment variable should be the IP address of your Docker host.
      # Nginx will use this to connect to your Next.js app if it's running on the host.
      - DOCKER_HOST_IP=${DOCKER_HOST_IP}
      # NGINX_PORT is used by the default Nginx template mechanism to specify the listen port.
      # We want Nginx inside the container to listen on port 80.
      - NGINX_PORT=80

volumes:
  postgresql: null
