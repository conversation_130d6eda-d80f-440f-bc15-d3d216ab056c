import { DiscordOrderMessage, OrderWithItems } from "@udoy/utils/types";
import { Env } from "../env";

export async function sendDiscordOrderMessage(order: DiscordOrderMessage) {
  // Customize your embed message
  const embed = {
    title: "অর্ডার এর বিবরন 🛒",
    color: 0xfaa85a, // Green color
    fields: [
      { name: "Order ID", value: order.id, inline: true },
      { name: "Customer", value: order.address.name, inline: true },
      { name: "Zone", value: order.address.zone.nam, inline: true },
      {
        name: "Total",
        value: `৳${(order.subTotal + order.shipping).toLocaleString("bn")}`,
        inline: true,
      },
      {
        name: "Items",
        value: order.orderItems
          .map(
            (item) =>
              `- ${
                item.product.nam || item.product.name
              } x${item.quantity.toLocaleString("bn")}`
          )
          .join("\n"),
      },
    ],
    timestamp: new Date().toISOString(),
  };

  try {
    fetch(Env.DISCORD_WEBHOOK_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        content: "আলহামদুলিল্লাহ, উদয় মার্টে নতুন অর্ডার এসেছে 📦",
        embeds: [embed],
      }),
    });
  } catch (error) {
    console.error("Failed to send Discord notification:", error);
  }
}
