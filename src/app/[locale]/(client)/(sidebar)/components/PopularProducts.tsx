import { getPrisma } from "@udoy/utils/db-utils";
import ProductItem from "../../components/ProductItem";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";
import { getLocale } from "next-intl/server";

async function getData() {
  "use cache";
  cacheTag(CacheKey.MostPopularProducts());
  cacheLife("days");

  const prisma = getPrisma();
  const products = await prisma.product.findMany({
    where: {
      hide: false,
      category: {
        hide: false,
      },
    },
    orderBy: {
      orderItems: {
        _count: "desc",
      },
    },
    take: 12,
    include: {
      images: true,
      unit: true,
      category: { select: { slug: true } },
    },
  });
  return products;
}

async function PopularProducts() {
  const products = await getData();
  const locale = await getLocale();
  return (
    <div className="mt-8 text-2xl font-bold w-full">
      <h3 className="text-center">Popular Products</h3>
      <div className="px-6 w-full grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 mt-6 xl:grid-cols-6 gap-4">
        {products.map((product) => (
          <ProductItem product={product} key={product.id} locale={locale} />
        ))}
      </div>
    </div>
  );
}

export default PopularProducts;
