import React from "react";
import { Metadata, ResolvingMetadata } from "next";
import { getPrisma } from "@udoy/utils/db-utils";
import { notFound } from "next/navigation";
import { Env } from "@udoy/libs/env";
import Image from "next/image";
import { Badge } from "@udoy/components/ui/badge";
import {
  Heart,
  Phone,
  RotateCcw,
  Share2,
  Shield,
  ShoppingCart,
  Star,
  Truck,
} from "lucide-react";
import { Button } from "@udoy/components/ui/button";
import { Card, CardContent } from "@udoy/components/ui/card";
import ProductActions from "./components/ProductActions";
import { UnitUtil } from "@udoy/utils/product-unit";
import CategoryBreadcrumb from "../../components/CategoryBreadcrumb";
import Locale from "@udoy/components/Locale/Client";

// Define the type for the params
type Props = {
  params: Promise<{ slug: string; product_slug: string; locale: string }>;
};

// Function to fetch product data
async function getProductData(categorySlug: string, productSlug: string) {
  const prisma = getPrisma();

  // Find the product by slug in the specified category
  const product = await prisma.product.findFirst({
    where: {
      slug: productSlug,
      category: {
        slug: categorySlug,
      },
    },
    include: {
      images: true,
      unit: true,
      category: true,
      company: true,
    },
  });

  return product;
}

// Generate metadata for the page
export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // Get the params
  const { slug, product_slug, locale } = await params;

  // Fetch product data
  const product = await getProductData(slug, product_slug);

  if (!product) {
    return {
      title: "Product Not Found",
      description: "The requested product could not be found.",
    };
  }

  // Check if we're in Bengali locale
  const isBangla = locale === "bn";

  // Get product name in appropriate language
  const productName = isBangla && product.nam ? product.nam : product.name;
  const categoryName =
    isBangla && product.category?.nam
      ? product.category.nam
      : product.category?.name;

  // Get product description in appropriate language
  const description =
    isBangla && product.biboron
      ? product.biboron
      : product.description ||
        `Buy ${product.name} online at UdoyMart. Best quality, affordable prices, and fast home delivery.`;

  // Get the first image URL or use a default
  const imageUrl =
    product.images && product.images.length > 0
      ? product.images[0].url
      : "/product-placeholder.png";

  // Format price in BDT
  const formattedPrice = `৳${product.price - product.discount}`;

  return {
    title: `${productName} | ${formattedPrice}`,
    description: description,
    openGraph: {
      title: `${productName} | UdoyMart`,
      description: description,
      images: [
        {
          url: imageUrl,
          width: 800,
          height: 600,
          alt: productName,
        },
      ],
      locale: isBangla ? "bn_BD" : "en_US",
      siteName: isBangla ? "উদয় মার্ট" : "UdoyMart",
      url: `${Env.NEXT_PUBLIC_FRONTEND_URL}/${locale}/${slug}/${product_slug}`,
    },
    twitter: {
      card: "summary_large_image",
      title: `${productName} | UdoyMart`,
      description: description,
      images: [imageUrl],
    },
    other: {
      ["og:type"]: "product",
      "product:price:amount": (product.price - product.discount).toString(),
      "product:price:currency": "BDT",
      "product:availability": product.supply > 0 ? "in stock" : "out of stock",
    },
  };
}

async function SingleProductPage({
  params,
}: {
  params: Promise<{ slug: string; product_slug: string; locale: string }>;
}) {
  const { slug, product_slug, locale } = await params;

  // Fetch product data
  const product = await getProductData(slug, product_slug);

  // If product not found, show 404
  if (!product) {
    return notFound();
  }

  return (
    <div className="">
      <CategoryBreadcrumb categorySlug={slug} />

      <div className="px-4 md:px-8 pb-6">
        <div className="grid lg:grid-cols-2 gap-8 mb-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden border">
              <Image
                src={product.images[0]?.url || "/placeholder.svg"}
                alt={product.name}
                fill
                className="object-cover"
                priority
              />
              {product.discount > 0 && (
                <Badge className="absolute top-4 left-4 bg-red-500 text-white">
                  {Math.round((product.discount / product.price) * 100)}% OFF
                </Badge>
              )}
            </div>

            {/* Thumbnail Images */}
            <div className="flex gap-2 overflow-x-auto pb-2">
              {product.images.map((image, index) => (
                <button
                  key={image.id}
                  //   onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    0 === index ? "border-blue-500" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image.url || "/placeholder.svg"}
                    alt={`${product.name} ${index + 1}`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                <Locale bn={product.nam}>{product.name}</Locale>
              </h1>

              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">(4.8) 124 reviews</span>
              </div>
            </div>

            {/* Price */}
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold">
                  ৳{product.price - product.discount}
                </span>
                {product.discount > 0 && (
                  <span className="text-xl text-gray-500 line-through">
                    ৳{product.price}
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">
                <Locale bn="প্রতি">Per</Locale>{" "}
                {UnitUtil.getAmountUnit(product.amount, product.unit, locale)} •{" "}
                <Locale bn="খরচ বাচল">You save</Locale> ৳{product.discount}
              </p>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  product.supply > 10 ? "bg-green-500" : "bg-orange-500"
                }`}
              />
              <span
                className={`text-sm font-medium ${
                  product.supply > 10 ? "text-green-600" : "text-orange-600"
                }`}
              >
                {product.supply > 10
                  ? "In Stock"
                  : `Only ${product.supply} left`}
              </span>
            </div>

            {/* Quantity Selector */}
            <ProductActions product={product} />

            {/* Trust Signals */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <Truck className="w-6 h-6 mx-auto mb-2 text-green-600" />
                <p className="text-xs text-gray-600">Fast Delivery</p>
              </div>
              <div className="text-center">
                <Shield className="w-6 h-6 mx-auto mb-2 text-blue-600" />
                <p className="text-xs text-gray-600">Quality Assured</p>
              </div>
              <div className="text-center">
                <RotateCcw className="w-6 h-6 mx-auto mb-2 text-purple-600" />
                <p className="text-xs text-gray-600">Easy Returns</p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Information Tabs */}
        <div className="bg-white rounded-lg border">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Product Details</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-gray-600 mb-4">{product.description}</p>
                <p className="text-gray-600">{product.biboron}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Specifications</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span>
                      {UnitUtil.getAmountUnit(product.amount, product.unit)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Brand:</span>
                    <span>{product.company?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Category:</span>
                    <span>{product.category?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Stock:</span>
                    <span>{product.supply} units available</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Support */}
        <Card className="mt-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold mb-1">
                  <Locale bn="সাহায্যের প্রয়োজন ?">Need Help?</Locale>
                </h3>
                <p className="text-sm text-gray-600">
                  <Locale bn="আমাদের সাথে যোগাযোগ করুন">
                    Contact our customer support team
                  </Locale>
                </p>
              </div>
              <a href="tel:01897546867">
                <Button variant="outline">
                  <Phone className="h-4 w-4 mr-2" />
                  <Locale bn="কল করুন">Call Now</Locale>
                </Button>
              </a>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org/",
            "@type": "Product",
            name: product.name,
            image: product.images.map((img) => img.url),
            description: product.description,
            brand: {
              "@type": "Brand",
              name: product.company?.name,
            },
            offers: {
              "@type": "Offer",
              url: `https://yourstore.com/product/${product.slug}`,
              priceCurrency: "BDT",
              price: product.price - product.discount,
              availability:
                product.supply > 0
                  ? "https://schema.org/InStock"
                  : "https://schema.org/OutOfStock",
              seller: {
                "@type": "Organization",
                name: "Your Store",
              },
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "124",
            },
          }),
        }}
      />
    </div>
  );
}

export default SingleProductPage;
