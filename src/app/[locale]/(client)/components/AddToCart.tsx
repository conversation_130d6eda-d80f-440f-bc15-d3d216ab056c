"use client";

import { Product } from "@prisma/client";
import { manageCart } from "@udoy/actions/cart";
import Button from "@udoy/components/Button";
import Hide from "@udoy/components/Hide";
import useCartUtils from "@udoy/hooks/useCartUtils";
import useStatus from "@udoy/hooks/useToastUtil";
import useUser from "@udoy/hooks/useUser";
import { store } from "@udoy/state";
import { localeNumber } from "@udoy/utils";
import { withError } from "@udoy/utils/app-error";
import { useSelector } from "@xstate/store/react";
import { Minus, Plus } from "lucide-react";
import { useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { toast } from "react-toastify";

function CartActions({ product }: { product: Product }) {
  const locale = useLocale();
  const count = useSelector(
    store,
    (state) => state.context.cartItems[product.id]?.count
  );
  const isBangla = locale === "bn";

  const { handleRemoveFromCart, handleAddToCart } = useCartUtils(product);

  return (
    <Hide
      open={!!count}
      fallback={
        <Button
          label={locale === "en" ? "Add to cart" : "কার্টে যুক্ত করুন"}
          className="w-full"
          onClick={handleAddToCart}
        />
      }
    >
      <div className="flex w-full">
        <Button
          className="rounded-r-none border-r max-[380px]:px-1.5"
          onClick={handleRemoveFromCart}
        >
          <Minus />
        </Button>
        <Button className="flex-1 rounded-none">
          <span className="hidden sm:block">
            {isBangla
              ? `ব্যাগে ${count?.toLocaleString(locale)} টি`
              : `${count?.toLocaleString(locale)} In Cart`}
          </span>
          <span className="sm:hidden">{count?.toLocaleString(locale)}</span>
        </Button>
        <Button
          className="rounded-l-none border-l max-[380px]:px-1.5"
          onClick={handleAddToCart}
        >
          <Plus />
        </Button>
      </div>
    </Hide>
  );
}

export default CartActions;
