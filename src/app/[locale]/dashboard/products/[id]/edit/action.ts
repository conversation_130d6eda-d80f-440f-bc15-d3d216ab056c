"use server";

import { Role } from "@prisma/client";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { productFormSchema } from "../../utils";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { zImage } from "@udoy/utils/zod";
import { createId } from "@paralleldrive/cuid2";
import { extname } from "path";
import { FileManager } from "@udoy/libs/backend/image-util";

export async function updateProduct(data: any, form: FormData) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const {
      removedImages,
      images: _,
      id,
      ...values
    } = productFormSchema.parse(data);

    if (!id) {
      return ActionError("Invalid Product");
    }

    const product = await prisma.product.update({
      where: { id: id },
      data: values,
      include: {
        category: true,
        unit: true,
        images: true,
      },
    });

    if (removedImages.length > 0) {
      const images = await getPrisma().productImage.findMany({
        where: { id: { in: removedImages } },
      });

      await Promise.all(images.map((img) => FileManager.imageRemove(img.url)));

      await getPrisma().productImage.deleteMany({
        where: { id: { in: removedImages } },
      });
    }

    const images = z.array(zImage).parse(form.getAll("images"));

    const imageUrls = await Promise.all(
      images.map(async (image) => {
        const cuid = createId();
        const fileExt = extname(image.name);
        const newFile = new File([image], `${cuid}${fileExt}`, {
          type: image.type,
        });
        const url = await FileManager.imageUpload(newFile, `products`);

        return { url, id: cuid };
      })
    );

    await prisma.product.update({
      where: { id: product.id },
      data: {
        images: { createMany: { data: imageUrls } },
      },
    });

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      return product;
    }

    return null;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Product");
  }
}
