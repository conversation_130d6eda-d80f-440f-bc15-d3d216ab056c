import { Suspense } from "react";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role as PrismaRole } from "@prisma/client";
import CustomersTable from "./components/customers-table";
import CustomersHeader from "./components/customers-header";
import CustomersFilters from "./components/customers-filters";
import Loading from "./loading";
import { buildQuery, FilterParams } from "./utils/queryBuilder";

export default async function PageWrapper({
  searchParams,
}: {
  searchParams: Promise<FilterParams>;
}) {
  const params = await searchParams;
  
  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <CustomersPage searchParams={params} />
    </Suspense>
  );
}

async function CustomersPage({ searchParams }: { searchParams: FilterParams }) {
  const prisma = getPrisma();
  const { whereClause, orderByClause, ...filterProps } = buildQuery(searchParams);

  // Get all users with their orders
  const users = await prisma.user.findMany({
    where: whereClause,
    orderBy: orderByClause,
    include: {
      orders: {
        include: {
          orderItems: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      },
      _count: {
        select: {
          orders: true,
        },
      },
    },
  });

  // Transform the data to match our Customer interface
  const customers = users.map((user) => {
    // Calculate total spent across all orders
    const totalSpent = user.orders.reduce((sum, order) => {
      return sum + order.subTotal + order.shipping;
    }, 0);

    // Get the date of the most recent order if any
    const lastOrderDate =
      user.orders.length > 0
        ? user.orders[0].createdAt.toISOString()
        : undefined;

    return {
      ...user,
      ordersCount: user._count.orders,
      totalSpent,
      lastOrderDate,
    };
  });

  return (
    <div className="flex flex-col gap-6">
      <CustomersHeader customers={customers} />
      <CustomersFilters {...filterProps} />
      <CustomersTable initialCustomers={customers} />
    </div>
  );
}
