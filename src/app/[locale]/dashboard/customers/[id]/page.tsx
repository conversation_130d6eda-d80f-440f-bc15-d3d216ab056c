import { Suspense } from "react";
import { notFound } from "next/navigation";
import { ArrowLeft, Mail, Phone, Calendar, Edit, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@udoy/components/ui/avatar";
import { Badge } from "@udoy/components/ui/badge";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";
import Link from "next/link";
import { getPrisma } from "@udoy/utils/db-utils";
import { Role } from "@prisma/client";
import { CustomerOrdersTable } from "../../components/customer-orders-table";
import { CustomerAddresses } from "../../components/customer-addresses";
import CustomerRoleDialog from "./components/customer-role-dialog";

// Helper function to get role badge variant
function getRoleBadgeVariant(role: Role) {
  switch (role) {
    case Role.SUPER_ADMIN:
      return "destructive";
    case Role.ADMIN:
      return "default";
    case Role.MAINTAINER:
      return "secondary";
    case Role.USER:
      return "outline";
    case Role.DELIVERY_MAN:
      return "warning";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(dateString: string | Date) {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date);
}

async function getCustomerData(customerId: number) {
  const prisma = getPrisma();

  const customer = await prisma.user.findUnique({
    where: { id: customerId },
    include: {
      address: {
        include: {
          zone: true,
        },
      },
      cart: true,
      orders: {
        include: {
          orderItems: {
            include: {
              product: {
                include: {
                  images: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      },
      _count: {
        select: {
          orders: true,
        },
      },
    },
  });

  if (!customer) {
    return null;
  }

  const zones = await prisma.deliveryZone.findMany({
    where: {
      parentZone: {
        slug: "mollahat",
      },
    },
    include: {
      subZones: true,
    },
  });

  // Calculate total spent across all orders
  const totalSpent = customer.orders.reduce((sum, order) => {
    return sum + order.subTotal + order.shipping;
  }, 0);

  // Get the date of the most recent order if any
  const lastOrderDate =
    customer.orders.length > 0 ? customer.orders[0].createdAt : null;

  return {
    ...customer,
    ordersCount: customer._count.orders,
    totalSpent,
    lastOrderDate,
    zones,
  };
}

export default async function CustomerDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: customerId } = await params;
  const customer = await getCustomerData(parseInt(customerId));

  if (!customer) {
    notFound();
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/dashboard/customers">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Customer Details</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Customer Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Profile</CardTitle>
              <CardDescription>Customer information</CardDescription>
            </div>
            <Button variant="outline" size="icon">
              <Edit className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <Avatar className="h-24 w-24 mb-4">
              <AvatarImage
                src={customer.avatar || "/placeholder.svg"}
                alt={customer.name || "Customer"}
              />
              <AvatarFallback className="text-2xl">
                {customer.name
                  ? customer.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                  : "U"}
              </AvatarFallback>
            </Avatar>
            <h2 className="text-xl font-bold">{customer.name}</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={getRoleBadgeVariant(customer.role) as any}>
                {customer.role.replace("_", " ")}
              </Badge>
              <CustomerRoleDialog customer={customer} />
            </div>
            <div className="w-full mt-6 space-y-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{customer.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{customer.phone || "Not provided"}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>Joined {formatDate(customer.createdAt)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Details Tabs */}
        <div className="md:col-span-2">
          <Tabs defaultValue="orders">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="addresses">Addresses</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>
            <TabsContent value="orders" className="mt-4 overflow-clip">
              <Card>
                <CardHeader>
                  <CardTitle>Order History</CardTitle>
                  <CardDescription>
                    This customer has placed {customer.ordersCount} orders
                    totaling ৳ {customer.totalSpent.toLocaleString()}
                  </CardDescription>
                </CardHeader>
                <CardContent className="">
                  <div className="max-h-[500px] overflow-y-auto ">
                    <CustomerOrdersTable orders={customer.orders} />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="addresses" className="mt-4">
              <Card>
                <div className="flex justify-between">
                  <CardHeader>
                    <CardTitle>Saved Addresses</CardTitle>
                    <CardDescription>
                      Manage customer&#39;s saved addresses
                    </CardDescription>
                  </CardHeader>
                  <div className="p-4">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Add New Address
                    </Button>
                  </div>
                </div>
                <CardContent>
                  <CustomerAddresses
                    addresses={customer.address}
                    currentAddressId={customer.cart?.addressId!}
                    zones={customer.zones}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="activity" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>
                    Customer&#39;s recent actions and events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    No recent activity to display
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Customer Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{customer.ordersCount}</div>
            <p className="text-xs text-muted-foreground">Total orders placed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Spending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              ৳{customer.totalSpent.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">Lifetime value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Last Order</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {customer.lastOrderDate
                ? formatDate(customer.lastOrderDate)
                : "N/A"}
            </div>
            <p className="text-xs text-muted-foreground">
              Most recent purchase
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
