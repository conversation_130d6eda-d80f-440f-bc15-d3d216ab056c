'use client';

import { useState } from "react";
import { RoleManagementDialog } from "../../components/role-management-dialog";
import { Role, User } from "@prisma/client";

export default function CustomersHeader({ 
  customers 
}: { 
  customers: User[] 
}) {
  const [customersList, setCustomersList] = useState<User[]>(customers);
  const [selectedCustomers, setSelectedCustomers] = useState<number[]>([]);

  // Handle bulk role change
  const handleBulkRoleChange = (customerIds: number[], newRole: Role) => {
    setCustomersList((prev) =>
      prev.map((customer) =>
        customerIds.includes(customer.id)
          ? { ...customer, role: newRole }
          : customer
      )
    );
  };

  return (
    <div className="flex items-center justify-between">
      <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
      <RoleManagementDialog
        customers={customersList}
        onRoleChange={handleBulkRoleChange}
        selectedCustomers={selectedCustomers}
      />
    </div>
  );
}