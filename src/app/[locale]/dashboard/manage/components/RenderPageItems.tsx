"use client";

import { Category, Product } from "@prisma/client";
import Hide from "@udoy/components/Hide";
import React from "react";
import { useLocale } from "next-intl";
import CategoryItem from "./CategoryItem";
import ProductItem from "./ProductItem";

function RenderPageItems(props: {
  categories: Category[];
  products: Product[];
  isBase: boolean;
}) {
  const [products] = React.useState(props.products);
  const locale = useLocale();

  return (
    <div className="px-4 md:px-8">
      <Hide
        open={props?.isBase}
        fallback={
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {props.categories.map((category) => (
              <CategoryItem key={category.id} category={category} />
            ))}
          </div>
        }
      >
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {products?.map((product) => (
            <ProductItem
              key={product.id}
              product={product as any}
              locale={locale}
            />
          ))}
        </div>
      </Hide>
    </div>
  );
}

export default RenderPageItems;
