"use client";

import { Category } from "@prisma/client";
import Hide from "@udoy/components/Hide";
import Locale from "@udoy/components/Locale/Client";
import { Button } from "@udoy/components/ui/button";
import { useIsBangla } from "@udoy/hooks/useIsBangla";
import { withError } from "@udoy/utils/app-error";
import { Edit, Eye, EyeOff } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { hideCategory } from "../action";
import { toast } from "sonner";
import { cn } from "@udoy/utils/shadcn";
import { useState } from "react";
import { CategoryEditDialog } from "./CategoryEditDialog";

function CategoryItem({ category }: { category: Category }) {
  const isBangla = useIsBangla();
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  async function handleHidecategory() {
    try {
      const result = await withError(hideCategory(category.id, !category.hide));
      if (result) {
        toast.success("Category hidden");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed To Hide Category");
    }
  }

  return (
    <div
      key={category.id}
      className={cn(
        "border border-white/10 rounded relative",
        category.hide && "opacity-50"
      )}
    >
      <div className="absolute right-2 top-2 flex gap-2">
        <Button
          size="icon"
          variant="outline"
          className=""
          onClick={handleHidecategory}
        >
          <Hide open={!category.hide} fallback={<Eye />}>
            <EyeOff />
          </Hide>
        </Button>
        <Button
          size="icon"
          variant="outline"
          className=""
          onClick={() => setEditDialogOpen(true)}
        >
          <Edit />
        </Button>
        <CategoryEditDialog 
          isOpen={editDialogOpen} 
          onClose={() => setEditDialogOpen(false)} 
          category={category} 
        />
      </div>
      <Link href={{ pathname: `/dashboard/manage/${category.slug}` }}>
        <div className="">
          <Image
            src={category?.image || ""}
            width={200}
            height={100}
            alt={isBangla ? category.nam! : category.name}
            className="object-cover w-full rounded-b-none rounded"
          />
        </div>
        <h3 className="mb-4 text-center px-4 font-semibold mt-4">
          <Locale bn={category.nam}>{category.name}</Locale>
        </h3>
      </Link>
    </div>
  );
}

export default CategoryItem;
