"use server";

import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { getSubcategories, productFormSchema } from "./utils";
import { Role } from "@prisma/client";
import { revalidatePath, revalidateTag } from "next/cache";
import { <PERSON><PERSON>K<PERSON> } from "@udoy/utils/cache-key";

export async function updateProduct(productId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const values = productFormSchema.parse(data);

    const product = await prisma.product.update({
      where: { id: productId },
      data: values,
      include: {
        category: true,
        unit: true,
        images: true,
      },
    });

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      revalidatePath(`/dashboard/shop/${product.category?.slug}`);
      return product;
    }

    return null;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Product");
  }
}

export async function hideCategory(categoryId: string, hide: boolean) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      // select: {
      //   id: true,
      //   isBase: true,
      //   slug: true,
      //   parentCategory: {
      //     select: {
      //       slug: true,
      //     },
      //   },
      //   subCategories: {
      //     select: {
      //       id: true,
      //       isBase: true,
      //       subCategories: {
      //         select: {
      //           isBase: true,
      //           id: true,
      //         },
      //       },
      //     },
      //   },
      // },
    });

    if (!category) {
      return ActionError("Invalid Category");
    }

    // const { subIds, baseIds } = getSubcategories(category as any);

    await prisma.category.update({
      where: { id: categoryId },
      data: { hide },
    });

    // await prisma.product.updateMany({
    //   where: { categoryId: { in: baseIds } },
    //   data: { hide },
    // });

    if (category) {
      revalidatePath(`/dashboard/manage/${category.slug}`);
      revalidateTag(CacheKey.ShopPage());
      revalidateTag(CacheKey.SidebarCategory());
      revalidateTag(CacheKey.MostPopularCategories());
      revalidateTag(CacheKey.MostPopularProducts());
      return true;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Hide Category");
  }
}

export async function updateCategory(categoryId: string, data: any) {
  try {
    const userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const category = await prisma.category.update({
      where: { id: categoryId },
      data: {
        name: data.name,
        nam: data.nam,
        slug: data.slug,
        isBase: data.isBase,
        featured: data.featured,
        position: data.position,
      },
      include: {
        parentCategory: {
          select: {
            slug: true,
          },
        },
      },
    });

    if (category) {
      revalidatePath(`/dashboard/manage/${category.slug}`);
      revalidateTag(CacheKey.SidebarCategory());
      revalidatePath(`/${category.slug}`);
      revalidatePath(`/${category.parentCategory?.slug}`);
      return category;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Update Category");
  }
}
