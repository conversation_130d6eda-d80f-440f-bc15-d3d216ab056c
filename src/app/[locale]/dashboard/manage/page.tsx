import { getPrisma } from "@udoy/utils/db-utils";
import React from "react";
import CategoryItem from "./components/CategoryItem";

async function getData() {
  const prisma = getPrisma();
  const categories = await prisma.category.findMany({
    where: { parentId: null },
    orderBy: [{ hide: "asc" }, { featured: "desc" }, { position: "asc" }],
  });

  return { categories };
}

async function Page() {
  const { categories } = await getData();
  return (
    <div
      className="flex-1 p-6 md:p-8 overflow-y-auto "
      style={{ maxHeight: "calc(100vh - 64px)" }}
    >
      <h1 className="text-3xl font-bold tracking-tight">Shop</h1>
      <p className="text-muted-foreground">
        Manage your shop&apos;s categories and products.
      </p>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-12 mt-10">
        {categories.map((category) => (
          <CategoryItem key={category.id} category={category} />
        ))}
      </div>
    </div>
  );
}

export default Page;
