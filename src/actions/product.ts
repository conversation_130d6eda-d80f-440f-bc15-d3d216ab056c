"use server";

import { FileManager } from "@udoy/libs/backend/image-util";
import { ActionError } from "@udoy/utils/app-error";
import { createSmallId } from "@udoy/utils/cuid";
import { getPrisma } from "@udoy/utils/db-utils";
import { wait } from "@udoy/utils/wait";
import { parseForm, zImage } from "@udoy/utils/zod";
import { redirect } from "next/navigation";
import { z } from "zod";
import { Prisma, Role } from "@prisma/client";
import { createId } from "@paralleldrive/cuid2";
import { getProducts } from "@udoy/libs/backend/product";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { revalidatePath } from "next/cache";
import { revalidateTag } from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";

const ProductSchema = z.object({
  name: z.string().min(1),
  nam: z.nullable(z.string()),
  biboron: z.nullable(z.string()),
  slug: z.string().min(1),
  price: z.number().min(1),
  sourcePrice: z.number().min(1),
  amount: z.number().min(1),
  categoryId: z.string().cuid().optional(),
  description: z.string().min(1),
  unitId: z.string().cuid(),
  images: z.array(zImage),
  productId: z.string().cuid().optional(),
  removedImages: z.array(z.string().cuid()).optional(),
});

const ProductUpdateSchema = ProductSchema.partial().extend({
  productId: z.string().cuid(),
  removedImages: z.array(z.string().cuid2()).optional(),
});

export type ProductSchema = z.infer<typeof ProductSchema>;

export async function createProduct(oldState: any, form: FormData) {
  let productId;
  try {
    const {
      price,
      name,
      description,
      sourcePrice,
      categoryId,
      images,
      unitId,
      amount,
      nam,
    } = ProductSchema.parse(parseForm(ProductSchema, form));

    const product = await getPrisma().product.create({
      data: {
        name,
        nam,
        description,
        price,
        sourcePrice,
        categoryId,
        amount,
        unitId,
        discount: 0,
        supply: 50,
      },
    });

    const imageUrls = await Promise.all(
      images.map(async (image, i) => {
        const cuid = createId();
        const url = await FileManager.imageUpload(
          image,
          `products/${product.id}/${cuid}`
        );

        return { url, id: cuid };
      })
    );

    await getPrisma().product.update({
      where: { id: product.id },
      data: {
        images: { createMany: { data: imageUrls } },
      },
    });

    productId = product.id;
  } catch (error: any) {
    console.log(error);
    return ActionError(error?.message || "Something Wnet Wrong");
  }
  redirect(`/dashboard/products/${productId}`);
}

export async function updateProduct(oldState: any, form: FormData) {
  try {
    const {
      images = [],
      removedImages = [],
      productId,
      slug,
      ...parsed
    } = ProductUpdateSchema.parse(parseForm(ProductSchema, form));

    const data: Prisma.ProductUpdateInput = parsed;

    if (images.length > 0) {
      const imageUrls = await Promise.all(
        images.map(async (image) => {
          const cuid = createId();
          const url = await FileManager.imageUpload(
            image,
            `products/${productId}/${cuid}`
          );

          return { url, id: cuid };
        })
      );

      data.images = { createMany: { data: imageUrls } };
    }

    if (removedImages.length > 0) {
      const images = await getPrisma().productImage.findMany({
        where: { id: { in: removedImages } },
      });

      await Promise.all(images.map((img) => FileManager.imageRemove(img.url)));

      data.images = {
        ...data.images,
        deleteMany: {
          id: { in: removedImages },
        },
      };
    }

    await getPrisma().product.update({
      where: { id: productId },
      data: { ...parsed },
    });
  } catch (error: any) {
    console.log(error);
    return ActionError(error?.message || "Something Wnet Wrong");
  }
}

export async function searchProducts(query: string) {
  try {
    const products = await getProducts(query);
    return products;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to search products");
  }
}

export async function searchProductsAdmin(query: string) {
  try {
    const products = await getProducts(query, true);
    return products;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to search products");
  }
}

export async function hideProduct(productId: string, hide: boolean) {
  try {
    const clientId = await CookieUtil.userId();
    const result = z.string().safeParse(productId);

    if (!result.success) {
      return ActionError("Invalid Product ID");
    }

    if (!clientId) {
      return ActionError("You are not loggedin");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: clientId,
        role: {
          in: [Role.ADMIN, Role.MAINTAINER, Role.SUPER_ADMIN],
        },
      },
    });

    if (!admin) {
      return ActionError("You are not authorized");
    }

    const product = await prisma.product.update({
      where: {
        id: productId,
      },
      data: {
        hide,
      },
      include: {
        category: {
          select: {
            id: true,
            hide: true,
            slug: true,
            parentCategory: {
              select: {
                id: true,
                parentCategory: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (product?.category?.hide === true) {
      const ids = [product.category.id];
      if (product.category.parentCategory) {
        ids.push(product.category.parentCategory.id);
      }
      if (product.category.parentCategory?.parentCategory) {
        ids.push(product.category.parentCategory.parentCategory.id);
      }

      await prisma.category.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data: {
          hide,
        },
      });
    }

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      revalidateTag(CacheKey.SidebarCategory());
      return true;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Hide Product!");
  }
}

export async function hideProductShopOwner(productId: string, hide: boolean) {
  try {
    const clientId = await CookieUtil.userId();
    const result = z.string().safeParse(productId);

    if (!result.success) {
      return ActionError("Invalid Product ID");
    }

    if (!clientId) {
      return ActionError("You are not loggedin");
    }

    const prisma = getPrisma();
    const owner = await prisma.user.findUnique({
      where: {
        id: clientId,
      },
    });

    const product = await prisma.product.update({
      where: {
        id: productId,
      },
      data: {
        hide,
      },
      include: {
        shop: {
          select: {
            id: true,
            ownerId: true,
          },
        },
        category: {
          select: {
            id: true,
            hide: true,
            slug: true,
            parentCategory: {
              select: {
                id: true,
                parentCategory: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (product?.shop?.ownerId !== owner?.id) {
      return ActionError("You are not the owner of this product");
    }

    if (product?.category?.hide === true) {
      const ids = [product.category.id];
      if (product.category.parentCategory) {
        ids.push(product.category.parentCategory.id);
      }
      if (product.category.parentCategory?.parentCategory) {
        ids.push(product.category.parentCategory.parentCategory.id);
      }

      await prisma.category.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data: {
          hide,
        },
      });
    }

    if (product) {
      revalidatePath(`/${product.category?.slug}`);
      revalidateTag(CacheKey.SidebarCategory());
      return true;
    }

    return false;
  } catch (error) {
    console.log(error);
    return ActionError("Failed To Hide Product!");
  }
}
