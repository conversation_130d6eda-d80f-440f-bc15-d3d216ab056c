# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

POSTGRES_PRISMA_URL="postgresql://user:pass@localhost:5433/udoy?schema=public"
POSTGRES_URL_NON_POOLING="postgresql://user:pass@localhost:5433/udoy?schema=public"
# DATABASE_PORT=5432
POSTGRES_USERNAME=user
POSTGRES_DATABASE=udoy
POSTGRES_PASS=pass
POSTGRES_ADMIN=adminpass

APP_SECRET=appsecret
GOOGLE_CLIENT_ID=************-2evu61dh0eikcfa8s1cke61iu9fikl6g.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_PROJECT_ID=udoymart-test
GOOGLE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
GOOGLE_TOKEN_URI=https://oauth2.googleapis.com/token

STATIC_DIR=static-dir
DOCKER_HOST_IP=*************
NGINX_PORT=2090
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
UPLOADTHING_SECRET="secret"
DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/1157000800800481310/1157000800800481310"
DISCORD_CICD_WEBHOOK="https://discord.com/api/webhooks/1157000800800481310/1157000800800481310"
MEILI_MASTER_KEY=your_secure_password_here
# Profiles: development, production
COMPOSE_PROFILES=development
NEXT_PUBLIC_VAPID_PUBLIC_KEY=BK6W5AqUhnE1XSczU7niChmhH9hOZl3GHJKWW7d9hFCNQXyvJv7dYnDTOW9A_hNPSWrj5ziCgeOVRAKTyRvVwPg
VAPID_PRIVATE_KEY=hlqplcij93NHpx4UZU2Z66Vz1tSRE195FLXQiECgqnM
